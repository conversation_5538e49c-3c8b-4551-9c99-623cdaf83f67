export async function up(knex) {
  // Function to get the LIC property_device ID for a given device ID
  await knex.raw(`
      CREATE OR REPLACE FUNCTION get_lic_for_device(p_device_id UUID)
      RETURNS UUID AS $$
      DECLARE
        v_lic_property_device_id UUID;
      BEGIN
        SELECT lic_property_device INTO v_lic_property_device_id
        FROM mesh_device_mapping
        WHERE mesh_property_device IN (
          SELECT id FROM property_device WHERE device = p_device_id AND end_date IS NULL
        )
        AND end_date IS NULL;
        RETURN v_lic_property_device_id;
      END;
      $$ LANGUAGE plpgsql;
    `);

  // Trigger for reservoir
  await knex.raw(`
      CREATE OR REPLACE FUNCTION check_reservoir_mesh()
      RETURNS TRIGGER AS $$
      DECLARE
        v_rm_lic_id UUID;
        v_pump_lic_id UUID;
      BEGIN
        IF NEW.reservoir_monitor IS NOT NULL AND NEW.water_pump IS NOT NULL THEN
          -- Get LIC for Reservoir Monitor
          SELECT get_lic_for_device(NEW.reservoir_monitor) INTO v_rm_lic_id;
          
          -- Get LIC for Water Pump Controller
          SELECT get_lic_for_device(wp.water_pump_controller) INTO v_pump_lic_id
          FROM water_pump wp WHERE wp.id = NEW.water_pump;

          IF v_rm_lic_id IS DISTINCT FROM v_pump_lic_id THEN
            RAISE EXCEPTION 'Reservoir Monitor and Service Water Pump must be in the same mesh network.';
          END IF;
        END IF;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      CREATE TRIGGER trigger_check_reservoir_mesh_before_insert
      BEFORE INSERT ON reservoir
      FOR EACH ROW EXECUTE FUNCTION check_reservoir_mesh();

      CREATE TRIGGER trigger_check_reservoir_mesh_before_update
      BEFORE UPDATE ON reservoir
      FOR EACH ROW EXECUTE FUNCTION check_reservoir_mesh();
    `);

  // Trigger for project
  await knex.raw(`
      CREATE OR REPLACE FUNCTION check_project_mesh()
      RETURNS TRIGGER AS $$
      DECLARE
        v_project_lic_property_device_id UUID;
        v_irrigation_pump_lic_property_device_id UUID;
        v_fertigation_pump_lic_property_device_id UUID;
      BEGIN
        IF NEW.localized_irrigation_controller IS NOT NULL THEN
          -- Get the LIC's property_device ID
          SELECT id INTO v_project_lic_property_device_id
          FROM property_device
          WHERE device = NEW.localized_irrigation_controller AND end_date IS NULL;

          -- Check irrigation pump
          IF NEW.irrigation_water_pump IS NOT NULL THEN
            SELECT get_lic_for_device(wp.water_pump_controller) INTO v_irrigation_pump_lic_property_device_id
            FROM water_pump wp WHERE wp.id = NEW.irrigation_water_pump;
            IF v_project_lic_property_device_id IS DISTINCT FROM v_irrigation_pump_lic_property_device_id THEN
              RAISE EXCEPTION 'Irrigation pump must be in the same mesh network as the project LIC.';
            END IF;
          END IF;

          -- Check fertigation pump
          IF NEW.fertigation_water_pump IS NOT NULL THEN
            SELECT get_lic_for_device(wp.water_pump_controller) INTO v_fertigation_pump_lic_property_device_id
            FROM water_pump wp WHERE wp.id = NEW.fertigation_water_pump;
            IF v_project_lic_property_device_id IS DISTINCT FROM v_fertigation_pump_lic_property_device_id THEN
              RAISE EXCEPTION 'Fertigation pump must be in the same mesh network as the project LIC.';
            END IF;
          END IF;
        END IF;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      CREATE TRIGGER trigger_check_project_mesh_before_insert
      BEFORE INSERT ON project
      FOR EACH ROW EXECUTE FUNCTION check_project_mesh();

      CREATE TRIGGER trigger_check_project_mesh_before_update
      BEFORE UPDATE ON project
      FOR EACH ROW EXECUTE FUNCTION check_project_mesh();
    `);

  // Trigger for sector
  await knex.raw(`
      CREATE OR REPLACE FUNCTION check_sector_mesh()
      RETURNS TRIGGER AS $$
      DECLARE
        v_project_lic_property_device_id UUID;
        v_vc_lic_property_device_id UUID;
      BEGIN
        -- Get Project's LIC property_device ID
        SELECT pd.id INTO v_project_lic_property_device_id
        FROM project p
        JOIN property_device pd ON pd.device = p.localized_irrigation_controller AND pd.end_date IS NULL
        WHERE p.id = NEW.project;

        IF v_project_lic_property_device_id IS NOT NULL THEN
          -- Get Valve Controller's LIC property_device ID
          SELECT get_lic_for_device(NEW.valve_controller) INTO v_vc_lic_property_device_id;

          IF v_project_lic_property_device_id IS DISTINCT FROM v_vc_lic_property_device_id THEN
            RAISE EXCEPTION 'Valve Controller must be in the same mesh network as the project LIC.';
          END IF;
        END IF;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      CREATE TRIGGER trigger_check_sector_mesh_before_insert
      BEFORE INSERT ON sector
      FOR EACH ROW EXECUTE FUNCTION check_sector_mesh();

      CREATE TRIGGER trigger_check_sector_mesh_before_update
      BEFORE UPDATE ON sector
      FOR EACH ROW EXECUTE FUNCTION check_sector_mesh();
    `);
}

export async function down(knex) {
  await knex.raw(
    "DROP TRIGGER IF EXISTS trigger_check_sector_mesh_before_update ON sector;"
  );
  await knex.raw(
    "DROP TRIGGER IF EXISTS trigger_check_sector_mesh_before_insert ON sector;"
  );
  await knex.raw("DROP FUNCTION IF EXISTS check_sector_mesh();");

  await knex.raw(
    "DROP TRIGGER IF EXISTS trigger_check_project_mesh_before_update ON project;"
  );
  await knex.raw(
    "DROP TRIGGER IF EXISTS trigger_check_project_mesh_before_insert ON project;"
  );
  await knex.raw("DROP FUNCTION IF EXISTS check_project_mesh();");

  await knex.raw(
    "DROP TRIGGER IF EXISTS trigger_check_reservoir_mesh_before_update ON reservoir;"
  );
  await knex.raw(
    "DROP TRIGGER IF EXISTS trigger_check_reservoir_mesh_before_insert ON reservoir;"
  );
  await knex.raw("DROP FUNCTION IF EXISTS check_reservoir_mesh();");

  await knex.raw("DROP FUNCTION IF EXISTS get_lic_for_device(UUID);");
}
