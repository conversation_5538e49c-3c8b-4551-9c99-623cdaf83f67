I need you to analyze a database migration file and update a seed script to comply with new constraints. Here are the specific steps:

1. **Read and analyze the migration file**: `/home/<USER>/geocontrol/repositorios/agro/irriga-mais/directus/migrations/20250813A-mesh-constraints.js`
   - Identify all new database constraints being added via triggers
   - Document what tables and fields are affected
   - Understand the business rules these constraints enforce

2. **Read and analyze the seed script**: `/home/<USER>/geocontrol/repositorios/agro/irriga-mais/directus/src/seed/index.ts`
   - Examine the data being seeded into the database
   - Identify any seed data that might violate the new constraints from the migration

3. **Identify constraint violations**: Compare the seed data against the new constraints to find:
   - Invalid data that violates the validation constraints

4. **Create a detailed plan**: Provide a specific plan to fix the seed script that includes:
   - Which specific seed data needs to be modified
   - What changes are required for each violation
   - The order in which changes should be made (considering dependencies)
   - Any new seed data that needs to be added to satisfy the validation constraints